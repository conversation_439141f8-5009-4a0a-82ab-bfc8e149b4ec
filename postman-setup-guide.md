# Postman JWT Token Automation Setup

## 1. Environment Setup

Create a new environment with these variables:
- `jwt_token` (empty initial value)
- `base_url` = `http://localhost:8000`

## 2. Login Request Configuration

**Request:** POST {{base_url}}/login

**Body (JSON):**
```json
{
    "username": "testuser",
    "password": "password"
}
```

**Tests Tab:**
```javascript
// Test if login was successful
pm.test("Login successful", function () {
    pm.response.to.have.status(200);
});

// Extract and save the token
if (pm.response.code === 200) {
    const responseJson = pm.response.json();
    
    // Save the token to environment variable
    pm.environment.set("jwt_token", responseJson.token);
    
    // Optional: Save user info
    if (responseJson.user) {
        pm.environment.set("user_info", JSON.stringify(responseJson.user));
    }
    
    console.log("✅ Token saved:", responseJson.token.substring(0, 50) + "...");
} else {
    console.log("❌ Login failed, token not saved");
}
```

## 3. Inventory Requests Configuration

**All inventory requests use:**
- **Authorization:** Bearer Token
- **Token:** `{{jwt_token}}`

### Get All Inventory
**Request:** GET {{base_url}}/inventory

### Create Inventory
**Request:** POST {{base_url}}/inventory
**Body (JSON):**
```json
{
    "name": "Test Item",
    "description": "Test Description",
    "quantity": 10,
    "price": 99.99,
    "category": "Test",
    "sku": "TEST-001"
}
```

### Update Inventory
**Request:** PUT {{base_url}}/inventory/1
**Body (JSON):**
```json
{
    "name": "Updated Item",
    "description": "Updated Description",
    "quantity": 20,
    "price": 199.99,
    "category": "Updated",
    "sku": "TEST-002"
}
```

### Delete Inventory
**Request:** DELETE {{base_url}}/inventory/1

## 4. Logout Request Configuration

**Request:** POST {{base_url}}/logout
**Authorization:** Bearer Token `{{jwt_token}}`

**Tests Tab (Optional - to clear token):**
```javascript
// Test if logout was successful
pm.test("Logout successful", function () {
    pm.response.to.have.status(200);
});

// Optional: Clear the token after logout
if (pm.response.code === 200) {
    pm.environment.unset("jwt_token");
    console.log("✅ Token cleared from environment");
}
```

## 5. Testing Flow

1. **Run Login** → Token automatically saved
2. **Run any inventory operation** → Should work
3. **Run Logout** → Token blacklisted
4. **Run inventory operation again** → Should get 401 Unauthorized

## 6. Debugging

Add to any request's **Pre-request Script**:
```javascript
console.log("Current token:", pm.environment.get("jwt_token"));
```

## 7. Collection-Level Authorization (Alternative)

Instead of setting authorization on each request:
1. Right-click collection → Edit
2. Authorization tab → Bearer Token
3. Token: `{{jwt_token}}`
4. All requests inherit this setting
